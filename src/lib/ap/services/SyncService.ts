import type {
  GetAPContactType,
  PostAPContactType,
  UpdateAPCustomfields,
} from "@libAP/APTypes";
import {
  apInvoiceCustomfields,
  apLTVCustomfield,
  apPaymentCustomfields,
  CustomFieldsNames,
} from "@libAP/CustomFieldsNames";
import { APContactRequest, APCustomFieldRequest } from "@libAP/requests";

import { eq } from "drizzle-orm";
import { LocalPatient, type TLocalPatient } from "@/lib/base/LocalPatient";
import { logger, removeHtmlTags } from "@/utils";

/**
 * Service for syncing data between CliniCore and AutoPatient
 * Handles custom fields, appointments, contacts, and related data synchronization
 */
export class APSyncService extends LocalPatient {
  private contactRequest: APContactRequest;
  private customFieldRequest: APCustomFieldRequest;

  constructor() {
    super();
    this.contactRequest = new APContactRequest();
    this.customFieldRequest = new APCustomFieldRequest();
  }

  /**
   * Sync CliniCore custom fields to AutoPatient
   * @param patient - Local patient record
   * @param extraFields - Additional fields to sync
   */
  async syncCCtoAPCustomfields(
    patient: TLocalPatient,
    extraFields: Record<string, unknown> = {}
  ): Promise<void> {
    if (!patient.apId || !patient.ccId) {
      throw new Error(
        "AP Contact ID or CC Patient ID is missing, unable to sync custom fields"
      );
    }

    // Get CC custom fields (this would need to be implemented with CC service)
    let ccNameValue = await this.getCCCustomfieldsLabelValue(patient);
    if (extraFields) {
      ccNameValue = { ...ccNameValue, ...extraFields };
    }

    // Add computed fields
    ccNameValue[CustomFieldsNames.Appointment.TotalAppointment] =
      patient.ccData?.appointments?.length || 0;
    ccNameValue[CustomFieldsNames.PatientID] = patient.ccData?.id;

    // Get service-specific data
    const services = await this.getIndividualServiceAppointmentCount(patient);
    const spends = await this.getIndividualServiceSpends(patient);

    if (Object.keys(services).length > 0) {
      ccNameValue = { ...ccNameValue, ...services };
    }
    if (Object.keys(spends).length > 0) {
      ccNameValue = { ...ccNameValue, ...spends };
    }

    const apNameId = await this.getApCustomfieldsNameId();
    const payload: PostAPContactType = {
      customFields: [],
    };

    if (Object.keys(ccNameValue).length > 0) {
      for (const [key, value] of Object.entries(ccNameValue)) {
        if (key in apNameId && payload.customFields) {
          payload.customFields.push({
            id: apNameId[key],
            value: value as string | number,
          });
        } else {
          logger.info(
            `No AP Custom Field found with Name ${key}, Creating it now.`
          );
          const cfRes = await this.customFieldRequest.create({
            name: key,
            dataType: "TEXT",
          });
          if (cfRes?.id) {
            payload.customFields?.push({
              id: cfRes.id,
              value: value as string | number,
            });
          }
        }
      }
    }

    if (payload.customFields && payload.customFields.length > 0) {
      const updateRes = await this.contactRequest.update(patient.apId, payload);
      if (updateRes) {
        await this.db
          .update(this.dbSchema.patient)
          .set({
            apData: updateRes,
            apUpdatedAt: new Date(),
          })
          .where(eq(this.dbSchema.patient.id, patient.id));
        logger.info(`CC to AP Customfields synced`, {
          contactId: patient.apId,
        });
      }
    } else {
      logger.info(`No customfields found to sync`, { contactId: patient.apId });
    }
  }

  /**
   * Get AP custom field name to ID mapping
   */
  async getApCustomfieldsNameId(): Promise<Record<string, string>> {
    const cf = await this.customFieldRequest.all();
    const nameId: Record<string, string> = {};
    if (cf.length > 0) {
      cf.forEach((c) => {
        nameId[c.name] = c.id;
      });
    }
    return nameId;
  }

  /**
   * Update or create contact in AutoPatient
   * @param patient - Local patient record
   * @param syncCustomfields - Whether to sync custom fields
   */
  async updateOrCreateContact(
    patient: TLocalPatient,
    syncCustomfields = true
  ): Promise<TLocalPatient> {
    if (!patient.ccData) {
      throw new Error("Required CC data is missing while creating contact");
    }
    if (!patient.email && !patient.phone) {
      throw new Error("Invalid contact data, email and phone is missing.");
    }

    const payload: PostAPContactType = {
      email: patient.email,
      phone: patient.phone,
      firstName: patient.ccData.firstName,
      lastName: patient.ccData.lastName,
      tags: ["cc_api"],
      dateOfBirth: patient.ccData.dob,
    };

    let apContact: GetAPContactType;
    if (!patient.apId) {
      payload.source = "cc";
      payload.gender = patient.ccData.gender;
      apContact = await this.contactRequest.upsert(payload);
    } else {
      payload.tags = [...(payload.tags ?? []), ...(patient.apData?.tags ?? [])];
      apContact = await this.contactRequest.update(patient.apId, payload);
    }

    if (apContact) {
      await this.db
        .update(this.dbSchema.patient)
        .set({
          apData: apContact,
          apId: apContact.id,
          apUpdatedAt: new Date(apContact.dateUpdated || new Date()),
        })
        .where(eq(this.dbSchema.patient.id, patient.id));

      logger.info(`Contact synced to AP`, {
        patientId: patient.id,
        apId: apContact.id,
      });
    } else {
      logger.error("Unable to create/update contact to AP", {
        ccData: patient.ccData,
      });
      throw new Error("Unable to create/update contact to AP");
    }

    if (syncCustomfields) {
      const updatedPatient = await this.getPatientById(patient.id);
      if (updatedPatient) {
        await this.syncCCtoAPCustomfields(updatedPatient);
      }
    }

    // Sync invoice and payments would be called here
    // await this.syncInvoicePayments(updatedPatient);

    const updatedPatient = await this.getPatientById(patient.id);
    if (!updatedPatient) {
      throw new Error("Failed to retrieve updated patient after contact sync");
    }
    return updatedPatient;
  }

  /**
   * Get AP custom field value by name for a contact
   */
  async getAPCustomFieldValueByName(
    contactId: string,
    fieldName: string
  ): Promise<string | null> {
    const contact = await this.contactRequest.get(contactId);
    const fId = await this.getAPCustomFieldIdByName(fieldName);
    return contact.customFields && contact.customFields.length > 0 && fId
      ? contact.customFields.find((cf) => cf.id === fId)?.value?.toString() ??
          null
      : null;
  }

  /**
   * Get AP custom field ID by name, creating if it doesn't exist
   */
  async getAPCustomFieldIdByName(name: string): Promise<string> {
    const fields = await this.customFieldRequest.all();
    const existingField = fields.find((cf) => cf.name === name);

    if (existingField) {
      return existingField.id;
    }

    const newField = await this.customFieldRequest.create({
      name,
      dataType: "TEXT",
    });
    return newField.id;
  }

  /**
   * Update AP custom fields for a patient
   */
  async updateApCustomfields(
    patient: TLocalPatient,
    customfields: UpdateAPCustomfields[]
  ): Promise<TLocalPatient> {
    if (!patient.apId) {
      logger.warn(`Contact AP Id is missing`, { ccId: patient.ccId });
      return patient;
    }

    const apCustomfields = await this.customFieldRequest.all();
    const payload: { id: string; value: string | number }[] = [];

    if (customfields.length > 0 && apCustomfields.length > 0) {
      for (const cf of customfields) {
        const match = apCustomfields.find((apcf) => apcf.name === cf.name);
        if (match) {
          payload.push({ id: match.id, value: cf.value });
        } else {
          const create = await this.customFieldRequest.create({
            name: cf.name,
            dataType: "TEXT",
          });
          if (create?.id) {
            payload.push({ id: create.id, value: cf.value });
          }
        }
      }
    }

    if (payload.length > 0) {
      const apRes = await this.contactRequest.update(patient.apId, {
        customFields: payload,
      });
      if (apRes?.id) {
        await this.db
          .update(this.dbSchema.patient)
          .set({
            apData: apRes,
            apUpdatedAt: new Date(),
          })
          .where(eq(this.dbSchema.patient.id, patient.id));
      }
    }

    const updatedPatient = await this.getPatientById(patient.id);
    if (!updatedPatient) {
      throw new Error(
        "Failed to retrieve updated patient after custom fields update"
      );
    }
    return updatedPatient;
  }

  /**
   * Sync invoice and payment data from CC to AP custom fields
   */
  async syncInvoicePayments(patient: TLocalPatient): Promise<TLocalPatient> {
    if (!patient.apId || !patient.ccId) {
      logger.warn(
        "Unable to sync invoice to AP, Contact ID or Patient ID is missing",
        {
          apId: patient.apId,
          ccId: patient.ccId,
        }
      );
      return patient;
    }

    // Refresh CC data first (would need CC service integration)
    // const ccContact = await ccPatientRequest.get(patient.ccId);
    // if (ccContact?.id) {
    //   await this.updatePatientCCData(patient, ccContact);
    // }

    const updatedPatient = await this.getPatientById(patient.id);
    if (!updatedPatient) return patient;

    await this.syncInvoice(updatedPatient);
    await this.syncPayment(updatedPatient);

    return (await this.getPatientById(patient.id)) || patient;
  }

  /**
   * Sync latest invoice data to AP custom fields
   */
  async syncInvoice(patient: TLocalPatient): Promise<TLocalPatient> {
    if (!patient.ccData?.invoices || patient.ccData.invoices.length === 0) {
      logger.info("No invoice to sync", {
        apId: patient.apId,
        ccId: patient.ccId,
      });
      return patient;
    }

    // This would need CC invoice service integration
    // const allInvoices = await ccInvoiceRequest.get(patient.ccData.invoices);
    // if (allInvoices && allInvoices.length > 0) {
    //   const latestInvoice = allInvoices[0];
    //   await this.syncLastInvoice(patient, latestInvoice);
    // }

    return patient;
  }

  /**
   * Sync payment data to AP custom fields
   */
  async syncPayment(patient: TLocalPatient): Promise<TLocalPatient> {
    if (!patient.ccData?.payments || patient.ccData.payments.length === 0) {
      logger.info("No payment to sync", {
        apId: patient.apId,
        ccId: patient.ccId,
      });
      return patient;
    }

    // This would need CC payment service integration
    // const allPayments = await ccPaymentRequest.get(patient.ccData.payments);
    // if (allPayments && allPayments.length > 0) {
    //   await this.syncLtv(patient, allPayments);
    //   return await this.syncLastPayment(patient, allPayments[0]);
    // }

    return patient;
  }

  /**
   * Calculate and sync Lifetime Value (LTV)
   */
  async syncLtv(
    patient: TLocalPatient,
    payments: Array<{
      canceled?: boolean;
      reversedBy?: unknown;
      reverses?: unknown;
      gross: number;
      patient: number;
    }>
  ): Promise<TLocalPatient> {
    let ltv = 0;
    payments.forEach((payment) => {
      if (
        !payment.canceled &&
        !payment.reversedBy &&
        !payment.reverses &&
        payment.gross > 0 &&
        payment.patient === patient.ccId
      ) {
        ltv += payment.gross;
      }
    });

    logger.info(`Contact LTV calculated`, { ccId: patient.ccId, ltv });
    return await this.updateApCustomfields(patient, [
      { name: apLTVCustomfield, value: ltv },
    ]);
  }

  /**
   * Sync latest invoice details to AP custom fields
   */
  async syncLastInvoice(
    patient: TLocalPatient,
    invoice: {
      positions?: Array<{
        gross: number;
        discount?: number;
        name: string;
      }>;
      diagnoses?: Array<{
        text: string;
      }>;
      pdfUrl?: string;
      status?: string;
      discount?: number;
    }
  ): Promise<TLocalPatient> {
    if (!invoice.positions || invoice.positions.length === 0) {
      return patient;
    }

    const totalAmount = invoice.positions.reduce(
      (a: number, b) => a + b.gross,
      0
    );
    const totalDiscount = invoice.positions.reduce(
      (a: number, b) => (b.discount ? a + b.discount : a),
      0
    );

    const productNames = invoice.positions.map((pos) => pos.name);
    const diagnosisNames = invoice.diagnoses
      ? invoice.diagnoses.map((diag) => diag.text)
      : [];

    const customFields: UpdateAPCustomfields[] = [
      {
        name: apInvoiceCustomfields.LatestInvoicePDFURL,
        value: invoice.pdfUrl || "",
      },
      {
        name: apInvoiceCustomfields.LastInvoiceGrossAmount,
        value: totalAmount,
      },
      { name: apInvoiceCustomfields.LastInvoiceDiscount, value: totalDiscount },
      {
        name: apInvoiceCustomfields.LastInvoiceTotalAmount,
        value: totalAmount - totalDiscount - (invoice.discount || 0),
      },
      {
        name: apInvoiceCustomfields.LatestPaymentStatus,
        value: invoice.status || "",
      },
      {
        name: apInvoiceCustomfields.LastInvoiceProducts,
        value: productNames.join(", "),
      },
      {
        name: apInvoiceCustomfields.LastInvoiceDiagnosis,
        value: diagnosisNames.join(", "),
      },
    ];

    // Add practitioner info if available (would need CC user service)
    // if (invoice.practicioner) {
    //   const practitioner = await ccUserRequest.get(invoice.practicioner);
    //   customFields.push({
    //     name: apInvoiceCustomfields.LastInvoiceTreatedBy,
    //     value: practitioner?.shortName || ''
    //   });
    // }

    await this.updateApCustomfields(patient, customFields);
    logger.info("Latest invoice updated", {
      apId: patient.apId,
      ccId: patient.ccId,
    });

    return patient;
  }

  /**
   * Sync latest payment details to AP custom fields
   */
  async syncLastPayment(
    patient: TLocalPatient,
    payment: {
      invoicePayments?: Array<{
        gross: number;
      }>;
      gross?: number;
      createdAt: string;
      pdfUrl?: string;
    }
  ): Promise<TLocalPatient> {
    if (!payment.invoicePayments?.length && !payment.gross) {
      return patient;
    }

    const latestPayment = payment.invoicePayments?.length
      ? payment.invoicePayments[payment.invoicePayments.length - 1].gross
      : payment.gross;

    const customFields: UpdateAPCustomfields[] = [
      { name: apPaymentCustomfields.LatestPaymentStatus, value: "Success" },
      {
        name: apPaymentCustomfields.LatestAmountPaid,
        value: Math.abs(latestPayment),
      },
      {
        name: apPaymentCustomfields.LatestPaymentDate,
        value: payment.createdAt,
      },
      {
        name: apPaymentCustomfields.LatestPaymentPDFURL,
        value: payment.pdfUrl || "",
      },
    ];

    await this.updateApCustomfields(patient, customFields);
    logger.info("Latest payment updated", {
      apId: patient.apId,
      ccId: patient.ccId,
    });

    return patient;
  }

  /**
   * Calculate individual service appointment counts
   */
  async individualServiceAppointmentCount(
    patient: TLocalPatient
  ): Promise<Record<string, number>> {
    if (
      !patient.ccData?.appointments ||
      patient.ccData.appointments.length === 0
    ) {
      logger.info("No appointments to sync individual counts", {
        apId: patient.apId,
        ccId: patient.ccId,
      });
      return {};
    }

    // This would need CC appointment and service integration
    // const allAppointments = await Promise.all(
    //   patient.ccData.appointments.map(id => ccAppointmentRequest.get(id))
    // );
    // const services = await ccServiceRequest.all();
    // const bookedServices: Record<string, number> = {};
    // const prefix = 'Total appointments booked for ';

    // allAppointments.forEach(appointment => {
    //   if (appointment.services?.length) {
    //     appointment.services.forEach(serviceId => {
    //       const service = services.find(s => s.id === serviceId);
    //       if (service) {
    //         const key = prefix + service.name;
    //         bookedServices[key] = (bookedServices[key] || 0) + 1;
    //       }
    //     });
    //   }
    // });

    logger.info("Individual service appointment count calculated");
    return {}; // Return empty for now, would return bookedServices
  }

  /**
   * Calculate individual service spending amounts
   */
  async individualServiceSpends(
    patient: TLocalPatient
  ): Promise<Record<string, number>> {
    if (!patient.ccData?.invoices || patient.ccData.invoices.length === 0) {
      logger.info("No invoices found for service spends calculation", {
        apId: patient.apId,
      });
      return {};
    }

    // This would need CC invoice service integration
    // const invoices = await ccInvoiceRequest.get(patient.ccData.invoices);
    // const spends: Record<string, number> = {};
    // const prefix = 'Total amount paid for ';

    // invoices.forEach(invoice => {
    //   if (invoice.positions?.length) {
    //     invoice.positions.forEach(position => {
    //       if (position.gross) {
    //         const key = prefix + position.name;
    //         spends[key] = (spends[key] || 0) + position.gross;
    //       }
    //     });
    //   }
    // });

    logger.info("Individual service spends calculated");
    return {}; // Return empty for now, would return spends
  }

  // Placeholder methods that would need CC service integration
  private async getCCCustomfieldsLabelValue(
    _patient: TLocalPatient
  ): Promise<Record<string, unknown>> {
    // This would integrate with CC service to get custom field values
    return {};
  }
}
